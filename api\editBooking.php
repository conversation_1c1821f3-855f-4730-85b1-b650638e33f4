<?php
// Set proper error reporting but don't display errors to users
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(array('status' => 'error', 'message' => 'Unauthorized access'));
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(array('status' => 'error', 'message' => 'Invalid request method'));
    exit;
}

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

try {
    // Check required fields
    $required_fields = ['id', 'useDate', 'useZone', 'name', 'phone', 'adult', 'child', 'infant', 'guide', 'foc', 'tl', 'voucher', 'agent', 'amount', 'remark', 'floor', 'table'];

    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }

    // Sanitize and validate inputs
    $id = intval($_POST['id']);
    if ($id <= 0) {
        throw new Exception('Invalid booking ID');
    }

    $useDate = sanitize_input($_POST['useDate']);
    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $useDate) && !strtotime($useDate)) {
        throw new Exception('Invalid date format');
    }
    $useDate = date('Y-m-d', strtotime($useDate)); // Convert to Y-m-d format

    $useZone = intval($_POST['useZone']);
    $name = sanitize_input($_POST['name']);
    if (empty($name)) {
        throw new Exception('Name cannot be empty');
    }

    $phone = sanitize_input($_POST['phone']);
    // Only keep digits in phone number
    $phone = preg_replace('/[^0-9]/', '', $phone);

    // Convert numeric values
    $adult = intval($_POST['adult']);
    $child = intval($_POST['child']);
    $infant = intval($_POST['infant']);
    $guide = intval($_POST['guide']);
    $foc = intval($_POST['foc']);
    $tl = intval($_POST['tl']);
    $amount = floatval($_POST['amount']);

    // Sanitize other fields
    $voucher = sanitize_input($_POST['voucher']);
    $agent = sanitize_input($_POST['agent']);
    $remark = sanitize_input($_POST['remark']);
    $floor = sanitize_input($_POST['floor']);
    $table = sanitize_input($_POST['table']);
    $specialRequest = isset($_POST['special_request']) ? (int)$_POST['special_request'] : 0;
    $bookingRound = isset($_POST['booking_round']) ? sanitize_input($_POST['booking_round']) : 'Sunset';

    // Include database connection
    include_once("../dbconnect/_dbconnect.php");

    // Update booking
    $returnValue = edit_booking($id, $useDate, $useZone, $name, $phone, $adult, $child, $infant, $guide, $foc, $tl, $voucher, $agent, $amount, $remark, $floor, $table, $specialRequest, $bookingRound);

    if ($returnValue) {
        echo json_encode(array('status' => 'success', 'message' => 'Booking updated successfully'));
    } else {
        throw new Exception('Failed to update booking');
    }

} catch (Exception $e) {
    echo json_encode(array('status' => 'error', 'message' => $e->getMessage()));
}
?>
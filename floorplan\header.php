<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="../assets/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="../assets/css/styles.css" />

  <!-- Table Styles -->
  <link rel="stylesheet" href="css/table-styles.css?v=1.0.7" />

  <!-- Floorplan Styles -->
  <link rel="stylesheet" href="css/floorplan.css?v=1.0.7" />

  <!-- <PERSON>trap Datepicker -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" />

  <!-- Agent Autocomplete Styles -->
  <link rel="stylesheet" href="css/agent-autocomplete.css" />

  <style>
    .swal2-confirm.btn-primary {
        background: #6a5cff;
        color: #fff;
        padding: 8px 24px;
        border-radius: 6px;
        font-weight: bold;
    }

    .swal2-cancel.btn-danger {
        background: #ff6a8b;
        color: #fff;
        padding: 8px 24px;
        border-radius: 6px;
        font-weight: bold;
    }

    /* Enhanced Modal Styling */
    #bookingModal .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    }

    #bookingModal .modal-header {
        background: white;
        color: #333;
        border-radius: 12px 12px 0 0;
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    #bookingModal .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
    }

    #bookingModal .btn-close {
        opacity: 0.6;
    }

    #bookingModal .btn-close:hover {
        opacity: 1;
    }

    #bookingModal .modal-body {
        padding: 1.5rem;
    }

    #bookingModal .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        color: #495057;
        font-weight: 600;
    }

    #bookingModal .form-control {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 0.6rem 0.75rem;
        font-size: 0.9rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    #bookingModal .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    #bookingModal .form-control[readonly] {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        color: #6c757d;
    }

    #bookingModal .form-control::placeholder {
        color: #adb5bd;
        font-style: italic;
    }

    #bookingModal .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    #bookingModal .form-check-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    #bookingModal .form-check-label {
        font-size: 0.9rem;
        color: #495057;
        cursor: pointer;
    }

    #bookingModal .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        border-radius: 4px;
        font-weight: 500;
    }

    #bookingModal .bg-light {
        background-color: #f8f9fa !important;
        border: 1px solid #e9ecef;
    }

    #bookingModal .modal-footer {
        border-top: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-radius: 0 0 12px 12px;
    }

    #bookingModal .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 6px;
        padding: 0.6rem 1.5rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    #bookingModal .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    #bookingModal .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        border-radius: 6px;
        padding: 0.6rem 1.5rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    #bookingModal .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        transform: translateY(-1px);
    }

    /* Number input styling */
    #bookingModal input[type="number"] {
        text-align: center;
    }

    /* Row spacing */
    #bookingModal .row.g-3 > * {
        margin-bottom: 0.75rem;
    }

    #bookingModal .form-label{
        margin-bottom: 0.25rem !important;
    }
    #bookingModal .form-control{
        padding: 0.3rem !important;
    }
    #bookingModal .modal-header{
        padding: 0.6rem 2rem !important;
    }
    .form-select{
        padding: 0.3rem !important;
    }
    .theme-tab.nav-tabs .nav-item .nav-link{
        padding: 0.5rem 1rem !important;
    }

</style>



  <title>Floor Plan</title>

  <!-- jQuery (must be loaded before Bootstrap) -->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

  <!-- Error Handler Script -->
  <script src="js/error-handler.js"></script>
</head>